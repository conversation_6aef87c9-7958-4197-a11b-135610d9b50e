/**
 * 文件管理器核心模块
 * 处理文件列表、文件操作等核心功能
 */

class FileManager {
    constructor() {
        this.currentFolder = null;
        this.currentFolderId = null;
        this.files = [];
        this.selectedFiles = new Set();
        this.selectedForDownload = new Set(); // 用于批量下载的选择
        // 获取保存的视图模式，如果是details则改为large-icons
        const savedViewMode = Utils.storage.get(CONFIG.STORAGE_KEYS.VIEW_MODE, 'large-icons');
        this.viewMode = savedViewMode === 'details' ? 'large-icons' : savedViewMode;
        this.layoutMode = Utils.storage.get(CONFIG.STORAGE_KEYS.LAYOUT_MODE, 'grid'); // 新增布局模式
        this.sortBy = Utils.storage.get(CONFIG.STORAGE_KEYS.SORT_ORDER, { field: 'name', order: 'asc' });
        this.breadcrumbs = [];
        this.isInFolder = false; // 标记是否在文件夹内
        this.isInSearchMode = false; // 标记是否在搜索模式
        this.searchResults = []; // 保存搜索结果
        this.favoriteStatusCache = {}; // 收藏状态缓存
        this.isShowingFavorites = false; // 是否显示收藏夹
        this.currentView = 'files'; // 当前视图类型

        this.init();
    }

    /**
     * 安全显示Toast消息
     */
    showToast(message, type = 'info') {
        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast[type](message);
        } else {
            CONFIG.log(type, message);
        }
    }

    /**
     * 初始化文件管理器
     */
    init() {
        this.bindEvents();
        this.loadSharedFolders();
        this.loadFiles();
        this.setupViewMode();

        // 延迟初始化收藏状态，确保DOM完全加载
        setTimeout(() => {
            this.refreshAllFavoriteStates();
        }, 500);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 布局切换
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                const layoutMode = e.target.closest('.layout-btn').dataset.layout;
                if (layoutMode) {
                    this.setLayoutMode(layoutMode);
                }
            });
        });

        // 视图切换
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const viewMode = e.target.closest('.view-btn').dataset.view;
                CONFIG.log('info', `视图按钮点击: ${viewMode}, 当前搜索模式: ${this.isInSearchMode}`);
                if (viewMode) {
                    this.setViewMode(viewMode);
                }
            });
        });
        
        // 排序
        const sortSelect = Utils.dom.$('#sort-select');
        if (sortSelect) {
            Utils.event.on(sortSelect, 'change', (e) => {
                const [field, order] = e.target.value.split('-');
                this.setSortOrder(field, order || 'asc');
            });
        }
        
        // 文件夹导航 - 双击进入文件夹
        Utils.event.on(document, 'dblclick', (e) => {
            const folderItem = e.target.closest('.folder-item');
            if (folderItem) {
                const folderId = folderItem.dataset.fileId;
                this.navigateToFolder(folderId);
            }
        });
        
        // 面包屑导航
        Utils.event.on(document, 'click', (e) => {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem) {
                e.preventDefault();

                // 如果在搜索模式下，清除搜索状态
                if (this.isInSearchMode) {
                    CONFIG.log('info', '面包屑导航：清除搜索模式');
                    this.clearSearchMode();
                }

                const folderId = breadcrumbItem.dataset.folderId;
                this.navigateToFolder(folderId || null);
            }
        });
        
        // 文件选择
        Utils.event.on(document, 'click', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileClick(fileItem, e);
            }
        });
        
        // 右键菜单
        Utils.event.on(document, 'contextmenu', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                e.preventDefault();
                this.showContextMenu(e.clientX, e.clientY, fileItem);
            }
        });
        
        // 双击文件 - 避免与文件夹双击冲突
        Utils.event.on(document, 'dblclick', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem && !fileItem.classList.contains('folder-item')) {
                this.handleFileDoubleClick(fileItem);
            }
        });

        // 文件操作按钮点击事件
        Utils.event.on(document, 'click', (e) => {
            console.log('=== 全局点击事件触发 ===');
            console.log('点击目标:', e.target);
            console.log('点击目标类名:', e.target.className);
            console.log('点击目标父元素:', e.target.parentElement);

            const actionBtn = e.target.closest('.action-btn');
            console.log('找到的操作按钮:', actionBtn);

            if (actionBtn) {
                console.log('操作按钮详情:', {
                    className: actionBtn.className,
                    dataset: actionBtn.dataset,
                    innerHTML: actionBtn.innerHTML
                });

                e.preventDefault();
                e.stopPropagation();

                const action = actionBtn.dataset.action;
                const fileItem = actionBtn.closest('.file-item');
                const fileId = fileItem?.dataset.fileId;

                console.log('=== 操作按钮点击详情 ===');
                console.log('action:', action);
                console.log('fileId:', fileId);
                console.log('fileItem:', fileItem);

                CONFIG.log('info', `操作按钮点击: action=${action}, fileId=${fileId}, timestamp=${Date.now()}`);

                if (!fileId) {
                    CONFIG.log('error', '未找到文件ID');
                    console.error('未找到文件ID，fileItem:', fileItem);
                    return;
                }

                switch (action) {
                    case 'download':
                        CONFIG.log('info', `执行下载: ${fileId}`);
                        this.downloadFile(fileId);
                        break;
                    case 'preview':
                        console.log('=== 执行预览操作 ===');
                        CONFIG.log('info', `执行预览: ${fileId}`);
                        this.previewFile(fileId);
                        break;
                    case 'favorite':
                        e.stopPropagation();
                        e.preventDefault();

                        // 防止重复点击
                        if (actionBtn.disabled) {
                            CONFIG.log('warn', '收藏按钮已禁用，忽略重复点击');
                            return;
                        }

                        this.favoriteFile(fileId);
                        break;
                    case 'open':
                        CONFIG.log('info', `执行打开: ${fileId}`);
                        this.navigateToFolder(fileId);
                        break;
                    case 'download-folder':
                        CONFIG.log('info', `执行文件夹下载: ${fileId}`);
                        this.downloadFolder(fileId);
                        break;
                    default:
                        CONFIG.log('warn', `未知操作: ${action}`);
                        console.warn('未知操作:', action);
                }
            } else {
                // 如果不是操作按钮，检查是否是图标点击
                if (e.target.tagName === 'I' && e.target.classList.contains('fa-eye')) {
                    console.log('=== 直接点击了眼睛图标 ===');
                    console.log('眼睛图标父元素:', e.target.parentElement);
                    const btn = e.target.parentElement;
                    if (btn && btn.classList.contains('action-btn')) {
                        console.log('触发父按钮点击');
                        btn.click();
                    }
                }
            }
        });

        // 侧边栏菜单点击
        Utils.event.on(document, 'click', (e) => {
            const menuLink = e.target.closest('.menu-item a[data-view]');
            if (menuLink) {
                e.preventDefault();
                const view = menuLink.dataset.view;
                CONFIG.log('info', `侧边栏菜单点击: ${view}`);

                switch (view) {
                    case 'favorites':
                        this.showFavorites();
                        break;
                    case 'home':
                        this.navigateToFolder(null);
                        break;
                    case 'downloads':
                        // TODO: 实现下载历史功能
                        this.showToast('下载历史功能开发中', 'info');
                        break;
                    default:
                        CONFIG.log('warn', `未知的菜单视图: ${view}`);
                }
            }
        });

        // 文件复选框事件
        Utils.event.on(document, 'change', (e) => {
            if (e.target.classList.contains('file-checkbox')) {
                e.stopPropagation();
                const fileId = e.target.dataset.fileId;
                this.handleFileCheckboxChange(fileId, e.target.checked);
            }
        });

        // 键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    
    /**
     * 加载共享文件夹
     */
    async loadSharedFolders() {
        try {
            // 检查FolderAPI是否可用
            if (typeof FolderAPI === 'undefined') {
                CONFIG.log('warn', 'FolderAPI not available, using empty folders list');
                this.renderSharedFolders([]);
                return;
            }

            // 检查getSharedFolders方法是否存在
            if (typeof FolderAPI.getSharedFolders !== 'function') {
                CONFIG.log('warn', 'FolderAPI.getSharedFolders not available, using empty folders list');
                this.renderSharedFolders([]);
                return;
            }

            const folders = await FolderAPI.getSharedFolders();
            this.renderSharedFolders(folders || []);
        } catch (error) {
            CONFIG.log('error', 'Failed to load shared folders:', error);

            this.showToast('加载共享文件夹失败', 'error');

            // 渲染空列表作为后备
            this.renderSharedFolders([]);
        }
    }
    
    /**
     * 渲染共享文件夹
     */
    renderSharedFolders(folders) {
        const container = Utils.dom.$('#shared-folders');
        if (!container) return;

        container.innerHTML = '';

        // 确保folders是数组
        if (!Array.isArray(folders)) {
            CONFIG.log('warn', 'folders is not an array:', folders);
            folders = [];
        }

        folders.forEach(folder => {
            // 确保folder对象有必要的属性
            if (!folder || typeof folder !== 'object') {
                CONFIG.log('warn', 'Invalid folder object:', folder);
                return;
            }

            const folderElement = Utils.dom.create('li', {
                className: 'folder-item',
                'data-folder-id': folder.id || '',
                innerHTML: `
                    <i class="fas fa-folder"></i>
                    <span>${folder.name || '未命名文件夹'}</span>
                `
            });

            container.appendChild(folderElement);
        });
    }
    
    /**
     * 加载文件列表
     */
    async loadFiles(folderId = null) {
        try {
            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.show(folderId ? '正在加载文件...' : '正在加载文件夹...');
            }

            CONFIG.log('info', `Loading files for folder: ${folderId}`);

            // 检查认证状态
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                this.showToast('请先登录', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return;
            }

            this.currentFolderId = folderId;
            this.isInFolder = !!folderId;
            this.isShowingFavorites = false;

            let files = [];
            let response = null;

            if (this.isInFolder) {
                // 在文件夹内：获取文件夹中的文件
                response = await FileAPI.getFiles(folderId);
                CONFIG.log('info', 'Files API Response:', response);

                // 处理不同的响应格式
                if (response && response.files) {
                    files = response.files;
                } else if (Array.isArray(response)) {
                    files = response;
                } else if (response && response.data && Array.isArray(response.data)) {
                    files = response.data;
                }

                // 在文件夹内：显示所有图片文件
                files = this.filterAllowedFiles(files);
            } else {
                // 在首页：获取共享文件夹列表
                const folders = await FolderAPI.getSharedFolders();
                CONFIG.log('info', 'Folders API Response:', folders);

                // 将文件夹转换为文件格式，添加type属性
                files = (folders || []).map(folder => ({
                    id: folder.id,
                    name: folder.name,
                    type: 'folder',
                    size: folder.statistics?.total_size || 0,
                    modified_at: folder.updated_at || folder.created_at,
                    file_count: folder.statistics?.file_count || folder.file_count || 0,
                    path: folder.path
                }));
            }

            CONFIG.log('info', `Processed ${files.length} items`);

            this.files = files;
            this.currentFolder = response?.folder || null;

            this.renderFiles();
            this.updateBreadcrumb();

            // 显示加载结果（只在首次加载或文件夹变化时显示）
            if (!this._lastLoadedFolderId || this._lastLoadedFolderId !== folderId) {
                if (files.length === 0) {
                    if (this.isInFolder) {
                        this.showToast('当前文件夹没有图片文件', 'info');
                    } else {
                        this.showToast('没有共享文件夹', 'info');
                    }
                } else {
                    if (this.isInFolder) {
                        this.showToast(`加载了 ${files.length} 个文件`, 'success');
                    } else {
                        this.showToast(`找到 ${files.length} 个文件夹`, 'success');
                    }
                }
                this._lastLoadedFolderId = folderId;
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load files:', error);

            // 更详细的错误信息
            let errorMessage = '加载失败';
            if (error.userMessage) {
                errorMessage = error.userMessage;
            } else if (error.message) {
                if (error.message.includes('401')) {
                    errorMessage = '请先登录';
                } else if (error.message.includes('403')) {
                    errorMessage = '没有访问权限';
                } else if (error.message.includes('404')) {
                    errorMessage = '文件夹不存在';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '无法连接到服务器';
                }
            }

            this.showToast(errorMessage, 'error');

            // 如果是认证错误，跳转到登录页
            if (error.message && (error.message.includes('401') || error.message.includes('请先登录'))) {
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        } finally {
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }
        }
    }



    /**
     * 生成缩略图HTML
     */
    generateThumbnailHTML(file, icon) {
        // 使用API缩略图
        const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');
        const uniqueId = `thumb-${file.id}-${Date.now()}`;

        return `
            <div class="thumbnail-container" id="${uniqueId}">
                <div class="thumbnail-loading">
                    <div class="loading-spinner-small"></div>
                </div>
                <img src="${thumbnailUrl}"
                     alt="${file.name}"
                     class="thumbnail-image"
                     onload="window.fileManager.onThumbnailLoad(this)"
                     onerror="window.fileManager.onThumbnailError(this)"
                     style="display:none;"
                     data-file-id="${file.id}"
                     data-file-name="${file.name}">
                <div class="thumbnail-fallback" style="display:none;">
                    <i class="${icon}"></i>
                </div>
            </div>
        `;
    }

    /**
     * 缩略图加载成功处理
     */
    onThumbnailLoad(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        if (loading) loading.style.display = 'none';
        img.style.display = 'block';

        CONFIG.log('info', `缩略图加载成功: ${img.dataset.fileName}`);
    }

    /**
     * 缩略图加载失败处理
     */
    onThumbnailError(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        const fallback = container.querySelector('.thumbnail-fallback');

        if (loading) loading.style.display = 'none';
        if (fallback) fallback.style.display = 'flex';

        CONFIG.log('warn', `缩略图加载失败: ${img.dataset.fileName}, URL: ${img.src}`);

        // 尝试重新加载（添加时间戳避免缓存）
        setTimeout(() => {
            const newUrl = img.src.includes('&retry=') ?
                img.src : `${img.src}&retry=${Date.now()}`;

            if (!img.dataset.retried) {
                img.dataset.retried = 'true';
                img.src = newUrl;
                CONFIG.log('info', `重试缩略图加载: ${img.dataset.fileName}`);
            }
        }, 1000);
    }

    /**
     * 预加载缩略图
     */
    preloadThumbnails(files) {
        // 只预加载图片文件的缩略图
        const imageFiles = files.filter(file =>
            Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        // 限制同时预加载的数量，避免过多请求
        const maxConcurrent = 6;
        let currentIndex = 0;

        const loadNext = () => {
            if (currentIndex >= imageFiles.length) return;

            const file = imageFiles[currentIndex++];
            const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');

            // 创建隐藏的图片元素来预加载
            const img = new Image();
            img.onload = () => {
                CONFIG.log('info', `缩略图预加载成功: ${file.name}`);
                loadNext();
            };
            img.onerror = () => {
                CONFIG.log('warn', `缩略图预加载失败: ${file.name}`);
                loadNext();
            };
            img.src = thumbnailUrl;
        };

        // 启动并发预加载
        for (let i = 0; i < Math.min(maxConcurrent, imageFiles.length); i++) {
            loadNext();
        }
    }

    /**
     * 过滤允许的文件 - 只显示图片格式
     */
    filterAllowedFiles(files) {
        return files.filter(file => {
            // 文件夹总是显示
            if (file.type === 'folder') {
                return true;
            }

            // 只显示允许的图片格式
            return CONFIG.FILES.isAllowedFile(file.name);
        });
    }
    
    /**
     * 渲染文件列表
     */
    renderFiles() {
        const sortedFiles = this.sortFiles(this.files);

        // 根据视图模式渲染
        switch (this.viewMode) {
            case 'extra-large-icons':
            case 'large-icons':
            case 'medium-icons':
            case 'small-icons':
                this.renderIconView(sortedFiles);
                break;
            default:
                this.renderIconView(sortedFiles);
        }

        // 更新视图模式按钮状态
        this.updateViewModeButtons();

        // 确保批量操作按钮存在（仅在文件夹内显示）
        if (this.isInFolder) {
            this.ensureBatchActionButtons();
        }
    }
    
    /**
     * 渲染图标视图
     */
    renderIconView(files) {
        const container = Utils.dom.$('#file-grid');
        if (!container) return;

        container.innerHTML = '';
        Utils.dom.show(container);
        Utils.dom.hide(Utils.dom.$('#file-list'));

        // 设置容器的CSS类以控制图标大小和布局
        let containerClasses = ['file-grid'];

        // 添加布局模式类
        if (this.layoutMode === 'horizontal' && !this.isInFolder) {
            // 在首页且为横向布局时，使用横向布局
            containerClasses.push('horizontal-layout');
        } else if (this.isInFolder) {
            // 在文件夹内，使用指定的视图模式
            containerClasses.push(this.viewMode);
        }
        // 在首页且为网格布局时，使用默认样式（不添加额外类）

        container.className = containerClasses.join(' ');

        files.forEach(file => {
            const fileElement = this.createFileIconItem(file);
            container.appendChild(fileElement);
        });

        // 预加载缩略图
        this.preloadThumbnails(files);

        // 确保所有收藏按钮状态正确初始化
        this.initializeFavoriteStates(files);
    }
    

    
    /**
     * 创建图标文件项
     */
    createFileIconItem(file) {
        const isImage = Utils.isImageFile(file.name);
        const isFolder = file.type === 'folder';
        const icon = CONFIG.FILES.getFileIcon(file.name, isFolder);
        const isFavorited = !isFolder && this.isFileFavorited(file.id);

        // 对所有图片文件显示缩略图（不限制在文件夹内）
        const showThumbnail = isImage && !isFolder;

        // 如果在收藏夹视图中，使用特殊的收藏文件项
        if (this.isShowingFavorites && !isFolder) {
            return this.createFavoriteFileItem(file);
        }

        const element = Utils.dom.create('div', {
            className: `file-item ${isFolder ? 'folder-item' : 'file-item'}`,
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                ${!isFolder ? `
                    <div class="file-checkbox-wrapper">
                        <input type="checkbox" class="file-checkbox" data-file-id="${file.id}">
                    </div>
                ` : ''}
                <div class="file-icon">
                    ${showThumbnail ?
                        this.generateThumbnailHTML(file, icon) :
                        `<i class="${icon}"></i>`
                    }
                </div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                ${!isFolder && this.viewMode !== 'small-icons' ? `
                    <div class="file-meta">
                        <span class="file-size">${Utils.formatFileSize(file.size)}</span>
                    </div>
                ` : ''}
                <div class="file-actions">
                    ${isFolder ? `
                        <button class="action-btn" data-action="open" title="打开">
                            <i class="fas fa-folder-open"></i>
                        </button>
                        <button class="action-btn" data-action="download-folder" title="下载文件夹">
                            <i class="fas fa-download"></i>
                        </button>
                    ` : `
                        <button class="action-btn" data-action="download" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="action-btn" data-action="preview" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn ${isFavorited ? 'favorited' : ''}" data-action="favorite" title="${isFavorited ? '取消收藏' : '收藏'}">
                            <i class="${isFavorited ? 'fas' : 'far'} fa-star"></i>
                        </button>
                    `}
                </div>
            `
        });

        return element;
    }
    

    
    /**
     * 获取文件类型文本 - 专门针对图片文件
     */
    getFileTypeText(filename) {
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));

        // 根据扩展名返回具体的图片类型
        const imageTypeMap = {
            '.jpg': 'JPEG图片',
            '.jpeg': 'JPEG图片',
            '.png': 'PNG图片',
            '.psd': 'Photoshop文档',
            '.tif': 'TIFF图片',
            '.tiff': 'TIFF图片',
            '.ai': 'Illustrator文档',
            '.eps': 'EPS矢量图',
            '.gif': 'GIF动图',
            '.bmp': 'BMP图片',
            '.webp': 'WebP图片',
            '.svg': 'SVG矢量图'
        };

        return imageTypeMap[ext] || '图片文件';
    }
    
    /**
     * 排序文件
     */
    sortFiles(files) {
        return [...files].sort((a, b) => {
            // 文件夹优先
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            let aValue, bValue;
            
            switch (this.sortBy.field) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'size':
                    aValue = a.size || 0;
                    bValue = b.size || 0;
                    break;
                case 'date':
                    aValue = new Date(a.modified_at);
                    bValue = new Date(b.modified_at);
                    break;
                case 'type':
                    aValue = this.getFileTypeText(a.name);
                    bValue = this.getFileTypeText(b.name);
                    break;
                default:
                    return 0;
            }
            
            if (aValue < bValue) return this.sortBy.order === 'asc' ? -1 : 1;
            if (aValue > bValue) return this.sortBy.order === 'asc' ? 1 : -1;
            return 0;
        });
    }
    
    /**
     * 设置视图模式
     */
    setViewMode(mode) {
        CONFIG.log('info', `设置视图模式: ${mode}, 搜索模式: ${this.isInSearchMode}, 搜索结果数量: ${this.searchResults.length}`);

        this.viewMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.VIEW_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            CONFIG.log('info', '保持搜索结果，不重新加载文件');
            this.files = this.searchResults;
        } else {
            CONFIG.log('info', '非搜索模式或无搜索结果，使用当前文件列表');
        }

        this.renderFiles();
    }

    /**
     * 设置布局模式
     */
    setLayoutMode(mode) {
        this.layoutMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.LAYOUT_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.layout === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置排序方式
     */
    setSortOrder(field, order = 'asc') {
        this.sortBy = { field, order };
        Utils.storage.set(CONFIG.STORAGE_KEYS.SORT_ORDER, this.sortBy);

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置视图模式
     */
    setupViewMode() {
        this.setViewMode(this.viewMode);
        this.setLayoutMode(this.layoutMode);
    }
    
    /**
     * 导航到文件夹
     */
    navigateToFolder(folderId) {
        // 重置加载标志，允许显示新的Toast消息
        this._lastLoadedFolderId = null;

        // 如果在搜索模式下，清除搜索状态
        if (this.isInSearchMode) {
            this.clearSearchMode();
        }

        // 如果在收藏夹视图中，清除收藏夹状态
        if (this.isShowingFavorites) {
            this.isShowingFavorites = false;
            this.currentView = null;
        }

        this.selectedFiles.clear();
        this.loadFiles(folderId);
    }

    /**
     * 清除搜索模式
     */
    clearSearchMode() {
        this.isInSearchMode = false;
        this.searchResults = [];

        // 同时清除搜索管理器的状态
        if (window.searchManager) {
            window.searchManager.isInSearchMode = false;
            window.searchManager.searchResults = [];
        }
    }

    /**
     * 更新视图模式按钮状态
     */
    updateViewModeButtons() {
        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === this.viewMode) {
                Utils.dom.addClass(btn, 'active');
            }
        });
    }
    
    /**
     * 更新面包屑导航
     */
    updateBreadcrumb() {
        const container = Utils.dom.$('.breadcrumb-nav');
        if (!container) return;

        container.innerHTML = `
            <a href="#" class="breadcrumb-item" data-folder-id="">
                <i class="fas fa-home"></i>
                首页
            </a>
        `;

        if (this.isInFolder && this.currentFolder) {
            container.innerHTML += ' <span class="breadcrumb-separator">/</span> ';
            const folderLink = Utils.dom.create('a', {
                className: 'breadcrumb-item active',
                'data-folder-id': this.currentFolder.id,
                textContent: this.currentFolder.name
            });
            container.appendChild(folderLink);
        }
    }
    
    /**
     * 处理文件点击
     */
    handleFileClick(fileItem, event) {
        const fileId = fileItem.dataset.fileId;
        
        if (event.ctrlKey || event.metaKey) {
            // 多选
            this.toggleFileSelection(fileId, fileItem);
        } else {
            // 单选
            this.clearSelection();
            this.selectFile(fileId, fileItem);
        }
    }
    
    /**
     * 处理文件双击
     */
    handleFileDoubleClick(fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        if (fileType === 'folder') {
            this.navigateToFolder(fileId);
        } else {
            this.previewFile(fileId);
        }
    }
    
    /**
     * 选择文件
     */
    selectFile(fileId, fileItem) {
        this.selectedFiles.add(fileId);
        Utils.dom.addClass(fileItem, 'selected');
    }
    
    /**
     * 切换文件选择状态
     */
    toggleFileSelection(fileId, fileItem) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            Utils.dom.removeClass(fileItem, 'selected');
        } else {
            this.selectedFiles.add(fileId);
            Utils.dom.addClass(fileItem, 'selected');
        }
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedFiles.clear();
        Utils.dom.$$('.file-item.selected').forEach(item => {
            Utils.dom.removeClass(item, 'selected');
        });
    }
    
    /**
     * 显示右键菜单
     */
    showContextMenu(x, y, fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        const menuItems = [
            {
                icon: 'fas fa-download',
                text: '下载',
                action: 'download',
                handler: () => this.downloadFile(fileId)
            },
            {
                icon: 'fas fa-eye',
                text: '预览',
                action: 'preview',
                handler: () => this.previewFile(fileId)
            },
            {
                icon: 'fas fa-star',
                text: '收藏',
                action: 'favorite',
                handler: () => this.favoriteFile(fileId)
            },
            {
                icon: 'fas fa-share',
                text: '分享',
                action: 'share',
                handler: () => this.shareFile(fileId)
            },
            { divider: true },
            {
                icon: 'fas fa-info-circle',
                text: '详细信息',
                action: 'info',
                handler: () => this.showFileInfo(fileId)
            }
        ];
        
        if (typeof Components !== 'undefined' && Components.ContextMenu) {
            Components.ContextMenu.show(x, y, menuItems);
        }
    }
    
    /**
     * 处理文件操作
     */
    handleFileAction(action, file) {
        const fileId = file.id;

        switch (action) {
            case 'download':
                this.downloadFile(fileId);
                break;
            case 'preview':
                this.previewFile(fileId);
                break;
            case 'open':
                if (file.type === 'folder') {
                    this.navigateToFolder(fileId);
                }
                break;
            default:
                CONFIG.log('warn', `Unknown file action: ${action}`);
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        try {
            // 使用新的单文件下载接口，确保所有下载都是压缩包形式
            const response = await fetch(`${CONFIG.API_BASE_URL}/api/download/single/${fileId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 423) {
                // 文件被加密，需要申请密码
                const errorData = await response.json();
                const fileName = errorData.filename || `文件_${fileId}`;
                this.showPasswordRequestDialog(fileId, fileName);
                return;
            }

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                const downloadData = result.data;

                if (downloadData.is_encrypted) {
                    // 文件已加密，显示密码申请对话框
                    this.showPasswordRequestDialog(fileId, downloadData.filename);
                } else {
                    // 文件未加密，直接下载
                    const downloadUrl = `${CONFIG.API_BASE_URL}${downloadData.download_url}`;
                    Utils.url.downloadFile(downloadUrl, downloadData.filename);
                    this.showToast('文件下载成功', 'success');

                    // 刷新下载记录
                    this.refreshDownloadRecords();
                }
            } else {
                throw new Error(result.error || '下载失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Download failed:', error);
            this.showToast('文件下载失败', 'error');
        }
    }
    
    /**
     * 预览文件
     */
    async previewFile(fileId) {
        try {
            console.log('=== previewFile 开始 ===');
            console.log('fileId:', fileId, 'typeof:', typeof fileId);
            console.log('this.files:', this.files);

            // 尝试多种匹配方式
            let file = this.files.find(f => f.id == fileId); // 使用 == 进行类型转换匹配

            console.log('找到的文件:', file);

            // 调试：显示所有文件的ID和类型
            console.log('所有文件的ID:', this.files.map(f => ({ id: f.id, type: typeof f.id, name: f.name })));

            if (!file) {
                console.error('未找到文件，fileId:', fileId);
                return;
            }

            console.log('文件名:', file.name);
            console.log('Utils.isImageFile 检查结果:', Utils.isImageFile(file.name));

            // 如果是图片文件，使用浮窗预览
            if (Utils.isImageFile(file.name)) {
                console.log('确认是图片文件，调用 showImagePreviewFloat');
                this.showImagePreviewFloat(fileId, file);
                return;
            } else {
                console.log('不是图片文件，使用模态框预览');
            }

            // 非图片文件使用原有的模态框预览
            const previewModal = Utils.dom.$('#preview-modal');
            const previewTitle = Utils.dom.$('#preview-title');
            const previewContainer = Utils.dom.$('#preview-container');

            if (!previewModal || !previewTitle || !previewContainer) return;

            previewTitle.textContent = file.name;
            previewContainer.innerHTML = '<div class="loading-spinner"><div class="spinner"></div></div>';

            if (typeof Components !== 'undefined' && Components.Modal) {
                Components.Modal.show('preview-modal');
            }

            if (Utils.isVideoFile(file.name)) {
                const video = Utils.dom.create('video', {
                    controls: true,
                    src: `${CONFIG.API.BASE_URL}/files/stream/${fileId}`,
                    style: 'max-width: 100%; max-height: 80vh;'
                });
                previewContainer.innerHTML = '';
                previewContainer.appendChild(video);
            } else {
                previewContainer.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-file"></i>
                        <p>此文件类型不支持预览</p>
                        <button class="btn btn-primary" onclick="fileManager.downloadFile('${fileId}')">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                `;
            }
        } catch (error) {
            CONFIG.log('error', 'Preview failed:', error);
            this.showToast('文件预览失败', 'error');
        }
    }

    /**
     * 显示图片预览浮窗
     */
    async showImagePreviewFloat(fileId, file) {
        try {
            console.log('=== showImagePreviewFloat 开始 ===');
            console.log('文件ID:', fileId);
            console.log('文件信息:', file);
            console.log('typeof Components:', typeof Components);
            console.log('Components:', Components);
            console.log('Components.ImagePreviewFloat:', Components ? Components.ImagePreviewFloat : 'Components未定义');

            // 检查浮窗组件是否可用
            if (typeof Components === 'undefined' || !Components.ImagePreviewFloat) {
                console.warn('ImagePreviewFloat component not available, fallback to modal');
                CONFIG.log('warn', 'ImagePreviewFloat component not available, fallback to modal');
                this.createImagePreview(fileId, file, Utils.dom.$('#preview-container'));
                return;
            }

            console.log('浮窗组件可用，开始获取图片URL');

            // 获取图片URL
            const imageUrl = await this.getImageUrlWithAuth(fileId);
            console.log('图片URL获取成功:', imageUrl);

            // 显示浮窗
            console.log('调用 Components.ImagePreviewFloat.show');
            Components.ImagePreviewFloat.show(imageUrl, file.name, fileId);

            // 更新浮窗中的收藏按钮状态
            setTimeout(() => {
                console.log('更新收藏按钮状态');
                Components.ImagePreviewFloat.updateFavoriteButton();
            }, 100);

            CONFIG.log('info', `图片预览浮窗已显示: ${file.name}`);
            console.log('=== showImagePreviewFloat 完成 ===');

        } catch (error) {
            console.error('showImagePreviewFloat 错误:', error);
            CONFIG.log('error', 'Failed to show image preview float:', error);
            this.showToast('图片预览失败', 'error');
        }
    }

    /**
     * 获取带认证的图片URL
     */
    async getImageUrlWithAuth(fileId) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                return URL.createObjectURL(blob);
            } else {
                // 如果预览失败，尝试缩略图
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
                const thumbnailResponse = await fetch(thumbnailUrl, {
                    headers: token ? {
                        'Authorization': `Bearer ${token}`
                    } : {}
                });

                if (thumbnailResponse.ok) {
                    const blob = await thumbnailResponse.blob();
                    return URL.createObjectURL(blob);
                } else {
                    throw new Error('无法获取图片数据');
                }
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to get image URL with auth:', error);
            throw error;
        }
    }

    /**
     * 创建图片预览
     */
    createImagePreview(fileId, file, container) {
        // 创建预览容器
        const previewWrapper = Utils.dom.create('div', {
            className: 'image-preview-wrapper'
        });

        // 创建工具栏
        const toolbar = Utils.dom.create('div', {
            className: 'preview-toolbar',
            innerHTML: `
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="zoom-out" title="缩小">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level">100%</span>
                    <button class="toolbar-btn" data-action="zoom-in" title="放大">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-fit" title="适应窗口">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-actual" title="实际大小">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="rotate-left" title="向左旋转">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="toolbar-btn" data-action="rotate-right" title="向右旋转">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="favorite" title="收藏">
                        <i class="${this.isFileFavorited(fileId) ? 'fas' : 'far'} fa-star"></i>
                    </button>
                    <button class="toolbar-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            `
        });

        // 创建图片容器
        const imageContainer = Utils.dom.create('div', {
            className: 'image-container'
        });

        // 创建图片元素
        const img = Utils.dom.create('img', {
            className: 'preview-image',
            alt: file.name,
            draggable: false
        });

        // 异步加载图片（带认证）
        this.loadImageWithAuth(fileId, img);

        // 预览状态
        const previewState = {
            scale: 1,
            rotation: 0,
            translateX: 0,
            translateY: 0,
            isDragging: false,
            lastX: 0,
            lastY: 0
        };

        // 更新图片变换
        const updateTransform = () => {
            const transform = `translate(${previewState.translateX}px, ${previewState.translateY}px) scale(${previewState.scale}) rotate(${previewState.rotation}deg)`;
            img.style.transform = transform;

            // 更新缩放显示
            const zoomLevel = toolbar.querySelector('.zoom-level');
            if (zoomLevel) {
                zoomLevel.textContent = Math.round(previewState.scale * 100) + '%';
            }
        };

        // 图片加载完成后的处理
        img.onload = () => {
            this.fitImageToContainer(img, imageContainer, previewState, updateTransform);
        };

        // 图片加载错误处理
        img.onerror = () => {
            // 如果预览失败，尝试使用缩略图
            img.src = FileAPI.getThumbnailURL(fileId, 'large');
            img.onerror = () => {
                // 如果缩略图也失败，显示错误信息
                container.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image"></i>
                        <p>图片预览失败</p>
                        <button class="btn btn-primary" onclick="fileManager.downloadFile('${fileId}')">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                `;
            };
        };

        // 绑定工具栏事件
        this.bindPreviewToolbarEvents(toolbar, img, previewState, updateTransform, fileId);

        // 绑定图片交互事件
        this.bindImageInteractionEvents(img, imageContainer, previewState, updateTransform);

        // 组装预览界面
        imageContainer.appendChild(img);
        previewWrapper.appendChild(toolbar);
        previewWrapper.appendChild(imageContainer);

        container.innerHTML = '';
        container.appendChild(previewWrapper);
    }
    
    /**
     * 收藏文件 - 完全重写版本
     */
    async favoriteFile(fileId) {
        // 立即返回，如果已经在处理中
        if (this._favoriteProcessing && this._favoriteProcessing.has(fileId)) {
            CONFIG.log('warn', `文件 ${fileId} 正在处理收藏操作，跳过重复请求`);
            return;
        }

        // 初始化处理集合
        if (!this._favoriteProcessing) {
            this._favoriteProcessing = new Set();
        }

        // 标记开始处理
        this._favoriteProcessing.add(fileId);
        CONFIG.log('info', `收藏操作锁定文件: ${fileId}`);

        // 获取当前状态（在try块外定义以便catch块访问）
        const currentFavorited = this.favoriteStatusCache[fileId] || false;

        try {
            CONFIG.log('info', `=== 开始收藏操作 文件ID: ${fileId} ===`);

            // 立即禁用所有相关按钮
            this._setFavoriteButtonsState(fileId, 'disabled');

            const expectedNewState = !currentFavorited;

            CONFIG.log('info', `当前状态: ${currentFavorited}, 预期新状态: ${expectedNewState}`);

            // 立即更新UI到预期状态
            this.favoriteStatusCache[fileId] = expectedNewState;
            this._updateFavoriteButtonsUI(fileId, expectedNewState);

            // 调用API
            const url = `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.FAVORITES_TOGGLE}`;
            // 获取认证信息
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;
            const requestBody = { file_id: parseInt(fileId) };

            CONFIG.log('info', `收藏API请求: ${url}`);
            CONFIG.log('info', `请求体: ${JSON.stringify(requestBody)}`);
            CONFIG.log('info', `Token: ${token ? '已设置' : '未设置'}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(requestBody)
            });

            CONFIG.log('info', `响应状态: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                const errorText = await response.text();
                CONFIG.log('error', `HTTP错误响应: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            CONFIG.log('info', `API响应:`, result);

            if (result.success) {
                // 确认最终状态
                const finalState = result.is_favorited;
                this.favoriteStatusCache[fileId] = finalState;
                this._updateFavoriteButtonsUI(fileId, finalState);

                // 显示成功消息
                const file = this.files.find(f => f.id == fileId);
                const fileName = file ? file.name : `文件${fileId}`;
                const action = result.action === 'added' ? '已收藏' : '已取消收藏';
                this.showToast(`${action} "${fileName}"`, result.action === 'added' ? 'success' : 'warning');

                CONFIG.log('info', `收藏操作成功: ${fileName} -> ${finalState}`);

                // 如果在收藏夹页面且取消了收藏，从列表中移除该项
                if (this.isShowingFavorites && result.action === 'removed') {
                    // 从当前文件列表中移除该文件
                    this.files = this.files.filter(file => file.id != fileId);

                    // 重新渲染文件列表
                    this.renderFiles();

                    // 更新显示消息
                    if (this.files.length === 0) {
                        this.showToast('收藏夹已清空', 'info');
                    } else {
                        this.showToast(`收藏夹还有 ${this.files.length} 个文件`, 'info');
                    }
                }

            } else {
                throw new Error(result.error || '收藏操作失败');
            }

        } catch (error) {
            CONFIG.log('error', `收藏操作失败:`, error);

            // 恢复到原始状态
            const originalState = currentFavorited;
            this.favoriteStatusCache[fileId] = originalState;
            this._updateFavoriteButtonsUI(fileId, originalState);

            this.showToast('收藏操作失败，请稍后重试', 'error');

        } finally {
            // 重新启用按钮
            setTimeout(() => {
                this._setFavoriteButtonsState(fileId, 'enabled');
                this._favoriteProcessing.delete(fileId);
                CONFIG.log('info', `收藏操作解锁文件: ${fileId}`);
            }, 300);
        }
    }

    /**
     * 设置收藏按钮状态
     */
    _setFavoriteButtonsState(fileId, state) {
        const buttons = document.querySelectorAll(`[data-file-id="${fileId}"] [data-action="favorite"]`);
        const previewBtn = document.querySelector('#preview-modal [data-action="favorite"]');

        const allButtons = [...buttons];
        if (previewBtn) allButtons.push(previewBtn);

        allButtons.forEach(btn => {
            if (state === 'disabled') {
                btn.disabled = true;
                btn.style.opacity = '0.5';
                btn.style.pointerEvents = 'none';
            } else {
                btn.disabled = false;
                btn.style.opacity = '1';
                btn.style.pointerEvents = 'auto';
            }
        });
    }

    /**
     * 更新收藏按钮UI
     */
    _updateFavoriteButtonsUI(fileId, isFavorited) {
        const buttons = document.querySelectorAll(`[data-file-id="${fileId}"] [data-action="favorite"]`);
        const previewBtn = document.querySelector('#preview-modal [data-action="favorite"]');

        const allButtons = [...buttons];
        if (previewBtn) allButtons.push(previewBtn);

        allButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = isFavorited ? 'fas fa-star' : 'far fa-star';
                btn.title = isFavorited ? '取消收藏' : '收藏';
                if (isFavorited) {
                    btn.classList.add('favorited');
                } else {
                    btn.classList.remove('favorited');
                }
            }
        });

        CONFIG.log('info', `UI更新完成: 文件${fileId} -> ${isFavorited ? '已收藏' : '未收藏'}`);
    }

    /**
     * 获取收藏列表
     */
    getFavorites() {
        try {
            const favoritesData = localStorage.getItem('fileShareFavorites');
            const favorites = favoritesData ? JSON.parse(favoritesData) : [];
            CONFIG.log('info', `获取收藏列表: ${favorites.length} 个项目`);
            return favorites;
        } catch (error) {
            CONFIG.log('error', 'Failed to get favorites:', error);
            // 尝试清除损坏的数据
            try {
                localStorage.removeItem('fileShareFavorites');
                CONFIG.log('info', '已清除损坏的收藏数据');
            } catch (e) {
                CONFIG.log('error', 'Failed to clear corrupted favorites:', e);
            }
            return [];
        }
    }

    /**
     * 保存收藏列表（已弃用，保留用于向后兼容）
     */
    saveFavorites(favorites) {
        try {
            // 不再保存到localStorage，仅记录日志
            CONFIG.log('info', `saveFavorites调用已弃用，数据现在存储在数据库中，忽略保存 ${favorites ? favorites.length : 0} 个项目`);
        } catch (error) {
            CONFIG.log('error', 'saveFavorites (deprecated):', error);
        }
    }

    /**
     * 更新收藏按钮状态 - 兼容旧代码
     */
    updateFavoriteButtonState(fileId, isFavorited) {
        this.favoriteStatusCache[fileId] = isFavorited;
        this._updateFavoriteButtonsUI(fileId, isFavorited);
    }

    /**
     * 检查文件是否已收藏
     */
    isFileFavorited(fileId) {
        // 优先使用缓存的收藏状态
        if (this.favoriteStatusCache && this.favoriteStatusCache[fileId] !== undefined) {
            return this.favoriteStatusCache[fileId];
        }

        // 如果没有缓存，返回false（将通过API异步更新）
        return false;
    }

    /**
     * 初始化所有文件的收藏状态
     */
    async initializeFavoriteStates(files) {
        CONFIG.log('info', '开始初始化收藏状态');

        try {
            // 获取所有文件的ID
            const fileIds = files
                .filter(file => file.type !== 'folder')
                .map(file => file.id);

            if (fileIds.length > 0) {
                // 批量检查收藏状态
                const statusResult = await FavoriteAPI.checkFavoriteStatus(fileIds);

                if (statusResult && statusResult.data) {
                    // 更新缓存，确保数据类型正确
                    const statusData = statusResult.data;
                    Object.keys(statusData).forEach(fileId => {
                        this.favoriteStatusCache[fileId] = Boolean(statusData[fileId]);
                    });

                    CONFIG.log('info', '收藏状态缓存已更新:', this.favoriteStatusCache);

                    // 延迟执行，确保DOM已完全渲染
                    setTimeout(() => {
                        files.forEach(file => {
                            if (file.type !== 'folder') {
                                const isFavorited = this.favoriteStatusCache[file.id] || false;
                                this.updateFavoriteButtonState(file.id, isFavorited);
                                CONFIG.log('info', `初始化文件 ${file.name} 收藏状态: ${isFavorited}`);
                            }
                        });
                        CONFIG.log('info', '收藏状态初始化完成');
                    }, 100);
                } else {
                    CONFIG.log('warn', '收藏状态API返回数据格式异常:', statusResult);
                }
            }
        } catch (error) {
            CONFIG.log('error', '初始化收藏状态失败:', error);

            // 回退到本地存储方式
            setTimeout(() => {
                files.forEach(file => {
                    if (file.type !== 'folder') {
                        const isFavorited = this.isFileFavorited(file.id);
                        this.updateFavoriteButtonState(file.id, isFavorited);
                        CONFIG.log('info', `初始化文件 ${file.name} 收藏状态: ${isFavorited} (本地)`);
                    }
                });
                CONFIG.log('info', '收藏状态初始化完成（本地模式）');
            }, 100);
        }
    }

    /**
     * 刷新所有收藏状态
     */
    refreshAllFavoriteStates() {
        CONFIG.log('info', '刷新所有收藏状态');

        const favoriteButtons = document.querySelectorAll('[data-action="favorite"]');
        CONFIG.log('info', `找到 ${favoriteButtons.length} 个收藏按钮`);

        favoriteButtons.forEach((btn, index) => {
            const fileItem = btn.closest('.file-item');
            if (fileItem) {
                const fileId = fileItem.dataset.fileId;
                if (fileId) {
                    const isFavorited = this.isFileFavorited(fileId);
                    this.updateFavoriteButtonState(fileId, isFavorited);
                    CONFIG.log('info', `刷新按钮 ${index + 1}: ${fileId} -> ${isFavorited}`);
                }
            }
        });
    }

    /**
     * 带认证的图片加载
     */
    async loadImageWithAuth(fileId, imgElement) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL（在图片加载完成后）
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果预览失败，尝试缩略图
            const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
            const thumbnailResponse = await fetch(thumbnailUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (thumbnailResponse.ok) {
                const blob = await thumbnailResponse.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果都失败了，触发错误处理
            if (imgElement.onerror) {
                imgElement.onerror();
            }

        } catch (error) {
            CONFIG.log('error', '带认证的图片加载失败:', error);
            if (imgElement.onerror) {
                imgElement.onerror();
            }
        }
    }

    /**
     * 适应图片到容器
     */
    fitImageToContainer(img, container, state, updateTransform) {
        const containerRect = container.getBoundingClientRect();
        const imgNaturalWidth = img.naturalWidth;
        const imgNaturalHeight = img.naturalHeight;

        if (imgNaturalWidth && imgNaturalHeight) {
            const containerRatio = containerRect.width / containerRect.height;
            const imageRatio = imgNaturalWidth / imgNaturalHeight;

            let scale;
            if (imageRatio > containerRatio) {
                // 图片更宽，以宽度为准
                scale = (containerRect.width * 0.9) / imgNaturalWidth;
            } else {
                // 图片更高，以高度为准
                scale = (containerRect.height * 0.9) / imgNaturalHeight;
            }

            state.scale = Math.min(scale, 1); // 不超过原始大小
            state.translateX = 0;
            state.translateY = 0;
            updateTransform();
        }
    }

    /**
     * 绑定预览工具栏事件
     */
    bindPreviewToolbarEvents(toolbar, img, state, updateTransform, fileId) {
        Utils.event.on(toolbar, 'click', (e) => {
            const btn = e.target.closest('.toolbar-btn');
            if (!btn) return;

            const action = btn.dataset.action;

            switch (action) {
                case 'zoom-in':
                    state.scale = Math.min(state.scale * 1.2, 5);
                    updateTransform();
                    break;

                case 'zoom-out':
                    state.scale = Math.max(state.scale / 1.2, 0.1);
                    updateTransform();
                    break;

                case 'zoom-fit':
                    this.fitImageToContainer(img, img.parentElement, state, updateTransform);
                    break;

                case 'zoom-actual':
                    state.scale = 1;
                    state.translateX = 0;
                    state.translateY = 0;
                    updateTransform();
                    break;

                case 'rotate-left':
                    state.rotation -= 90;
                    updateTransform();
                    break;

                case 'rotate-right':
                    state.rotation += 90;
                    updateTransform();
                    break;

                case 'favorite':
                    e.stopPropagation();
                    e.preventDefault();
                    this.favoriteFile(fileId);
                    break;

                case 'download':
                    this.downloadFile(fileId);
                    break;
            }
        });
    }

    /**
     * 绑定图片交互事件
     */
    bindImageInteractionEvents(img, container, state, updateTransform) {
        // 鼠标拖拽
        Utils.event.on(img, 'mousedown', (e) => {
            e.preventDefault();
            state.isDragging = true;
            state.lastX = e.clientX;
            state.lastY = e.clientY;
            img.style.cursor = 'grabbing';
        });

        Utils.event.on(document, 'mousemove', (e) => {
            if (!state.isDragging) return;

            const deltaX = e.clientX - state.lastX;
            const deltaY = e.clientY - state.lastY;

            state.translateX += deltaX;
            state.translateY += deltaY;
            state.lastX = e.clientX;
            state.lastY = e.clientY;

            updateTransform();
        });

        Utils.event.on(document, 'mouseup', () => {
            if (state.isDragging) {
                state.isDragging = false;
                img.style.cursor = 'grab';
            }
        });

        // 鼠标滚轮缩放
        Utils.event.on(container, 'wheel', (e) => {
            e.preventDefault();

            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newScale = state.scale * delta;

            if (newScale >= 0.1 && newScale <= 5) {
                state.scale = newScale;
                updateTransform();
            }
        });

        // 双击重置
        Utils.event.on(img, 'dblclick', () => {
            this.fitImageToContainer(img, container, state, updateTransform);
        });

        // 设置初始光标
        img.style.cursor = 'grab';
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        const loadingDiv = document.getElementById('loading-indicator') || this.createLoadingIndicator();
        loadingDiv.querySelector('.loading-text').textContent = message;
        loadingDiv.style.display = 'flex';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingDiv = document.getElementById('loading-indicator');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    /**
     * 创建加载指示器
     */
    createLoadingIndicator() {
        const loadingDiv = Utils.dom.create('div', {
            id: 'loading-indicator',
            className: 'loading-indicator',
            innerHTML: `
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            `,
            style: 'display: none;'
        });
        document.body.appendChild(loadingDiv);
        return loadingDiv;
    }

    /**
     * 显示收藏夹
     */
    async showFavorites() {
        try {
            CONFIG.log('info', 'Loading favorites from database...');

            // 重置Toast标志
            this._favoriteToastShown = false;

            // 显示加载状态
            this.showLoading('正在加载收藏夹...');

            // 从数据库获取收藏列表（增加页面大小以显示所有收藏）
            const result = await FavoriteAPI.getFavorites(1, 1000);

            CONFIG.log('info', 'Favorites API result:', result);

            // 处理API返回的数据结构
            let favorites = [];
            let totalCount = 0;
            let currentPage = 1;
            let totalPages = 1;

            if (result && result.data && result.data.favorites) {
                favorites = result.data.favorites;
                totalCount = result.data.total_count || 0;
                currentPage = result.data.page || 1;
                totalPages = result.data.total_pages || 1;
            } else if (result && result.favorites) {
                favorites = result.favorites;
                totalCount = favorites.length;
            } else {
                CONFIG.log('warn', 'Unexpected API response structure:', result);
            }

            CONFIG.log('info', `收藏分页信息: 当前页 ${currentPage}/${totalPages}, 总数 ${totalCount}, 当前页显示 ${favorites.length} 个`);

            if (favorites && favorites.length >= 0) {

                // 设置当前状态
                this.currentFolderId = null;
                this.isInFolder = false;
                this.isShowingFavorites = true;
                this.currentView = 'favorites';

                // 转换收藏数据为文件格式
                this.files = favorites.map(fav => {
                    const fileData = fav.file || fav;
                    return {
                        id: fileData.id,
                        name: fileData.filename || fileData.name,
                        type: 'file',
                        size: fileData.file_size || fileData.size,
                        modified_at: fileData.file_modified || fileData.modified_at || fileData.modified_time,
                        extension: fileData.extension,
                        is_image: fileData.is_image,
                        folder_id: fileData.folder_id,
                        favorited_at: fav.favorited_at,
                        folder_name: fileData.folder_name || (fileData.folder ? fileData.folder.name : '未知文件夹'),
                        // 保留原始收藏信息
                        favorite_id: fav.id,
                        favorite_notes: fav.notes
                    };
                });

                this.renderFiles();

                // 显示结果（只在首次加载时显示）
                if (!this._favoriteToastShown) {
                    if (favorites.length === 0) {
                        this.showToast('您还没有收藏任何文件', 'info');
                    } else {
                        let message = `找到 ${favorites.length} 个收藏的文件`;
                        if (totalCount > favorites.length) {
                            message += ` (共 ${totalCount} 个，当前显示第 ${currentPage} 页)`;
                        }
                        this.showToast(message, 'success');
                    }
                    this._favoriteToastShown = true;
                }

            } else {
                CONFIG.log('error', 'Invalid favorites response:', result);
                this.showToast('获取收藏列表失败', 'error');

                // 设置空状态
                this.files = [];
                this.isShowingFavorites = true;
                this.currentView = 'favorites';
                this.renderFiles();
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load favorites:', error);

            if (error.message && error.message.includes('fetch')) {
                this.showToast('网络连接失败，无法加载收藏夹', 'error');
            } else {
                this.showToast('加载收藏夹失败，请稍后重试', 'error');
            }

            // 设置空状态
            this.files = [];
            this.isShowingFavorites = true;
            this.currentView = 'favorites';
            this.renderFiles();

        } finally {
            this.hideLoading();
        }
    }

    /**
     * 创建收藏文件项
     */
    createFavoriteFileItem(file) {
        const isImage = Utils.isImageFile(file.name);
        const icon = CONFIG.FILES.getFileIcon(file.name, false);
        const showThumbnail = isImage;

        const element = Utils.dom.create('div', {
            className: 'file-item favorite-item',
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                <div class="file-icon">
                    ${showThumbnail ?
                        this.generateThumbnailHTML(file, icon) :
                        `<i class="${icon}"></i>`
                    }
                </div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-meta">
                    <span class="file-size">${Utils.formatFileSize(file.size)}</span>
                    <span class="file-folder">来自: ${file.folder_name || '未知文件夹'}</span>
                    <span class="favorite-date">收藏于: ${Utils.formatDate(file.favorited_at)}</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn favorited" data-action="favorite" title="取消收藏">
                        <i class="fas fa-star"></i>
                    </button>
                </div>
            `
        });

        return element;
    }

    /**
     * 分享文件
     */
    shareFile(fileId) {
        // TODO: 实现分享功能
        this.showToast('分享功能开发中...', 'info');
    }

    /**
     * 显示文件信息
     */
    showFileInfo(fileId) {
        // TODO: 实现文件信息功能
        this.showToast('文件信息功能开发中...', 'info');
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboard(event) {
        // Ctrl+A 全选
        if (event.ctrlKey && event.key === 'a') {
            event.preventDefault();
            this.selectAll();
        }
        
        // Delete 删除
        if (event.key === 'Delete') {
            this.deleteSelected();
        }
        
        // Escape 取消选择
        if (event.key === 'Escape') {
            this.clearSelection();
        }
    }
    
    /**
     * 全选文件
     */
    selectAll() {
        this.clearSelection();
        Utils.dom.$$('.file-item').forEach(item => {
            const fileId = item.dataset.fileId;
            this.selectFile(fileId, item);
        });
    }

    /**
     * 处理文件复选框变化
     */
    handleFileCheckboxChange(fileId, checked) {
        if (checked) {
            this.selectedForDownload.add(fileId);
        } else {
            this.selectedForDownload.delete(fileId);
        }

        // 更新批量操作按钮状态
        this.updateBatchActionButtons();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActionButtons() {
        const selectedCount = this.selectedForDownload.size;

        // 如果没有批量操作按钮，创建它们
        this.ensureBatchActionButtons();

        const batchDownloadBtn = Utils.dom.$('#batch-download-btn');
        const selectAllBtn = Utils.dom.$('#select-all-btn');
        const clearSelectionBtn = Utils.dom.$('#clear-selection-btn');

        if (batchDownloadBtn) {
            batchDownloadBtn.disabled = selectedCount === 0;
            batchDownloadBtn.textContent = selectedCount > 0 ?
                `下载选中的 ${selectedCount} 个文件` : '批量下载';
        }

        if (selectAllBtn) {
            selectAllBtn.style.display = selectedCount === 0 ? 'inline-block' : 'none';
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.style.display = selectedCount > 0 ? 'inline-block' : 'none';
        }
    }

    /**
     * 确保批量操作按钮存在
     */
    ensureBatchActionButtons() {
        // 检查是否已经存在批量操作按钮
        if (Utils.dom.$('#batch-download-btn')) return;

        // 查找视图控制区域
        const viewControls = Utils.dom.$('.view-controls');
        if (!viewControls) return;

        // 创建批量操作按钮容器
        const batchActionsContainer = Utils.dom.create('div', {
            className: 'batch-actions',
            innerHTML: `
                <button id="select-all-btn" class="btn btn-secondary">
                    <i class="fas fa-check-square"></i>
                    全选
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary" style="display: none;">
                    <i class="fas fa-times"></i>
                    取消选择
                </button>
                <button id="batch-download-btn" class="btn btn-primary" disabled>
                    <i class="fas fa-download"></i>
                    批量下载
                </button>
            `
        });

        viewControls.appendChild(batchActionsContainer);

        // 绑定事件
        Utils.event.on(Utils.dom.$('#select-all-btn'), 'click', () => this.selectAllForDownload());
        Utils.event.on(Utils.dom.$('#clear-selection-btn'), 'click', () => this.clearDownloadSelection());
        Utils.event.on(Utils.dom.$('#batch-download-btn'), 'click', () => this.batchDownload());
    }

    /**
     * 全选文件用于下载
     */
    selectAllForDownload() {
        // 选中所有文件（不包括文件夹）
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = true;
            const fileId = checkbox.dataset.fileId;
            this.selectedForDownload.add(fileId);
        });

        this.updateBatchActionButtons();
    }

    /**
     * 清除下载选择
     */
    clearDownloadSelection() {
        // 取消选中所有复选框
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        this.selectedForDownload.clear();
        this.updateBatchActionButtons();
    }

    /**
     * 批量下载文件
     */
    async batchDownload() {
        if (this.selectedForDownload.size === 0) {
            this.showToast('请先选择要下载的文件', 'warning');
            return;
        }

        try {
            this.showToast('正在准备下载...', 'info');

            const fileIds = Array.from(this.selectedForDownload);

            // 使用新的批量下载接口
            const response = await fetch(`${CONFIG.API_BASE_URL}/api/download/batch`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ file_ids: fileIds })
            });

            if (response.status === 423) {
                // 批量文件被加密，需要申请密码
                const errorData = await response.json();
                this.showToast('部分文件已加密，请单独下载并申请密码', 'warning');
                return;
            }

            if (!response.ok) {
                throw new Error(`批量下载失败: ${response.status}`);
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            // 获取文件名
            const contentDisposition = response.headers.get('content-disposition');
            let filename = `batch_download_${Date.now()}.zip`;
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) filename = matches[1];
            }

            Utils.url.downloadFile(url, filename);
            URL.revokeObjectURL(url);

            this.showToast(`成功下载 ${fileIds.length} 个文件`, 'success');

            // 清除选择
            this.clearDownloadSelection();

            // 刷新下载记录
            this.refreshDownloadRecords();

        } catch (error) {
            CONFIG.log('error', 'Batch download failed:', error);
            this.showToast('批量下载失败', 'error');
        }
    }

    /**
     * 下载文件夹
     */
    async downloadFolder(folderId) {
        try {
            this.showToast('正在打包文件夹...', 'info');

            // 调用后端API下载文件夹
            const response = await FileAPI.downloadFolder(folderId);

            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                // 获取文件名
                const contentDisposition = response.headers.get('content-disposition');
                let filename = `folder_${folderId}_${Date.now()}.zip`;
                if (contentDisposition) {
                    const matches = contentDisposition.match(/filename="(.+)"/);
                    if (matches) filename = matches[1];
                }

                Utils.url.downloadFile(url, filename);
                URL.revokeObjectURL(url);

                this.showToast('文件夹下载成功', 'success');
            } else {
                throw new Error('文件夹下载失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Folder download failed:', error);
            this.showToast('文件夹下载失败', 'error');
        }
    }
    
    /**
     * 删除选中的文件
     */
    async deleteSelected() {
        if (this.selectedFiles.size === 0) return;
        
        let confirmed = false;
        if (typeof Components !== 'undefined' && Components.Confirm) {
            confirmed = await Components.Confirm.show(
                `确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`,
                '确认删除'
            );
        } else {
            confirmed = confirm(`确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`);
        }
        
        if (confirmed) {
            // TODO: 实现删除功能
            this.showToast('删除功能开发中...', 'info');
        }
    }
    
    /**
     * 刷新文件列表
     */
    refresh() {
        // 根据当前视图状态决定刷新方式
        if (this.isShowingFavorites) {
            // 如果在收藏夹视图，刷新收藏列表
            this.showFavorites();
        } else if (this.isInSearchMode) {
            // 如果在搜索模式，重新执行搜索
            if (window.searchManager && window.searchManager.lastSearchQuery) {
                window.searchManager.performSearch(window.searchManager.lastSearchQuery);
            } else {
                this.loadFiles(this.currentFolder?.id);
            }
        } else {
            // 正常文件夹视图，刷新文件列表
            this.loadFiles(this.currentFolder?.id);
        }
    }

    /**
     * 显示Toast消息
     */
    showToast(message, type = 'info') {
        // 防重复显示机制
        if (!this._lastToastMessages) {
            this._lastToastMessages = new Map();
        }

        const messageKey = `${type}:${message}`;
        const now = Date.now();
        const lastShown = this._lastToastMessages.get(messageKey);

        if (lastShown && (now - lastShown) < 3000) {
            console.log(`FileManager Toast重复消息被阻止: ${message}`);
            return;
        }

        this._lastToastMessages.set(messageKey, now);

        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast.show(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 刷新单个文件的缩略图
     */
    refreshThumbnail(fileId) {
        const thumbnailContainers = Utils.dom.$$(`[id^="thumb-${fileId}"]`);
        thumbnailContainers.forEach(container => {
            const img = container.querySelector('.thumbnail-image');
            const loading = container.querySelector('.thumbnail-loading');
            const fallback = container.querySelector('.thumbnail-fallback');

            if (img && loading && fallback) {
                // 重置状态
                loading.style.display = 'block';
                img.style.display = 'none';
                fallback.style.display = 'none';

                // 重新加载缩略图（添加时间戳避免缓存）
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'medium');
                img.src = `${thumbnailUrl}&t=${Date.now()}`;
            }
        });
    }

    /**
     * 批量刷新缩略图
     */
    refreshAllThumbnails() {
        const imageFiles = this.files.filter(file =>
            Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        imageFiles.forEach(file => {
            this.refreshThumbnail(file.id);
        });

        this.showToast('正在刷新缩略图...', 'info');
    }

    /**
     * 创建文件夹卡片
     */
    createFolderCard(file) {
        const card = Utils.dom.create('div', {
            className: 'folder-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件夹图标
        const iconDiv = Utils.dom.create('div', {
            className: 'folder-icon'
        });
        const icon = Utils.dom.create('i', {
            className: 'fas fa-folder'
        });
        iconDiv.appendChild(icon);

        // 文件夹名称
        const nameDiv = Utils.dom.create('div', {
            className: 'folder-name',
            textContent: file.name,
            title: file.name
        });

        card.appendChild(iconDiv);
        card.appendChild(nameDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.folder-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');

            // 触发文件夹打开事件
            this.handleFileAction('open', file);
        });

        // 添加双击事件
        card.addEventListener('dblclick', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleFileAction('open', file);
        });

        return card;
    }

    /**
     * 创建文件详情卡片
     */
    createFileDetailCard(file) {
        const isImage = Utils.isImageFile(file.name);
        const icon = CONFIG.FILES.getFileIcon(file.name, false);

        const card = Utils.dom.create('div', {
            className: 'file-detail-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件图标
        const iconDiv = Utils.dom.create('div', {
            className: 'file-icon'
        });
        const iconElement = Utils.dom.create('i', {
            className: icon
        });
        iconDiv.appendChild(iconElement);

        // 文件信息
        const infoDiv = Utils.dom.create('div', {
            className: 'file-info'
        });

        // 文件名
        const nameDiv = Utils.dom.create('div', {
            className: 'file-name',
            textContent: file.name,
            title: file.name
        });

        // 文件元数据
        const metaDiv = Utils.dom.create('div', {
            className: 'file-meta'
        });

        // 修改时间
        const timeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const timeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '修改时间'
        });
        const timeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.modified || '-'
        });
        timeItem.appendChild(timeLabel);
        timeItem.appendChild(timeValue);

        // 文件类型
        const typeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const typeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '类型'
        });
        const typeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: this.getFileTypeDescription(file.name)
        });
        typeItem.appendChild(typeLabel);
        typeItem.appendChild(typeValue);

        // 文件大小
        const sizeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const sizeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '大小'
        });
        const sizeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.size || '-'
        });
        sizeItem.appendChild(sizeLabel);
        sizeItem.appendChild(sizeValue);

        metaDiv.appendChild(timeItem);
        metaDiv.appendChild(typeItem);
        metaDiv.appendChild(sizeItem);

        infoDiv.appendChild(nameDiv);
        infoDiv.appendChild(metaDiv);

        // 操作按钮
        const actionsDiv = Utils.dom.create('div', {
            className: 'file-actions'
        });

        // 下载按钮
        const downloadBtn = Utils.dom.create('button', {
            className: 'action-btn',
            'data-action': 'download',
            title: '下载'
        });
        downloadBtn.innerHTML = '<i class="fas fa-download"></i>';
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleFileAction('download', file);
        });

        // 预览按钮（仅图片）
        if (isImage) {
            const previewBtn = Utils.dom.create('button', {
                className: 'action-btn',
                'data-action': 'preview',
                title: '预览'
            });
            previewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            previewBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleFileAction('preview', file);
            });
            actionsDiv.appendChild(previewBtn);
        }

        // 收藏按钮
        const favoriteBtn = Utils.dom.create('button', {
            className: 'action-btn',
            'data-action': 'favorite',
            title: '收藏'
        });
        favoriteBtn.innerHTML = '<i class="fas fa-star"></i>';
        // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）

        actionsDiv.appendChild(downloadBtn);
        actionsDiv.appendChild(favoriteBtn);

        card.appendChild(iconDiv);
        card.appendChild(infoDiv);
        card.appendChild(actionsDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.file-detail-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');
        });

        return card;
    }

    /**
     * 获取文件类型描述
     */
    getFileTypeDescription(fileName) {
        const ext = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'jpg': 'JPG 图片文件',
            'jpeg': 'JPEG 图片文件',
            'png': 'PNG 图片文件',
            'gif': 'GIF 图片文件',
            'bmp': 'BMP 图片文件',
            'tif': 'TIF 图片文件',
            'tiff': 'TIFF 图片文件',
            'psd': 'PSD 图片文件',
            'ai': 'AI 图片文件',
            'eps': 'EPS 图片文件'
        };
        return typeMap[ext] || `${ext.toUpperCase()} 文件`;
    }

    /**
     * 显示密码申请对话框
     */
    showPasswordRequestDialog(fileId, fileName, isFromRecord = false) {
        // 创建对话框HTML
        const dialogHtml = `
            <div class="password-request-dialog">
                <div class="dialog-header">
                    <h3>申请解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-request-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="file-info">
                        <i class="fas fa-file-archive"></i>
                        <span class="file-name">${fileName}</span>
                    </div>
                    <div class="info-text">
                        <p>该文件已加密，需要密码才能解压。</p>
                        <p>请填写申请原因（可选）：</p>
                    </div>
                    <textarea class="reason-input" placeholder="请简要说明申请密码的原因..." maxlength="200"></textarea>
                    <div class="remaining-requests">
                        <span class="remaining-count">剩余申请次数：<span id="remaining-count">-</span></span>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary" onclick="this.closest('.password-request-dialog').remove()">
                        取消
                    </button>
                    <button class="btn btn-primary" onclick="fileManager.submitPasswordRequest(${fileId}, this, ${isFromRecord})">
                        申请密码
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 聚焦到文本框
        const reasonInput = overlay.querySelector('.reason-input');
        if (reasonInput) {
            reasonInput.focus();
        }

        // 添加ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        });
    }

    /**
     * 提交密码申请
     */
    async submitPasswordRequest(fileId, buttonElement, isFromRecord = false) {
        try {
            const dialog = buttonElement.closest('.password-request-dialog');
            const reasonInput = dialog.querySelector('.reason-input');
            const reason = reasonInput.value.trim();

            // 禁用按钮
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 申请中...';

            // 调用API申请密码
            const result = await FileAPI.requestDownloadPassword(fileId, reason);

            if (result.success) {
                // 显示密码
                this.showPasswordResult(result.data.password, result.data.remaining_requests);

                // 关闭申请对话框
                dialog.closest('.dialog-overlay').remove();

                this.showToast('密码申请成功！', 'success');
            } else {
                throw new Error(result.error || '密码申请失败');
            }

        } catch (error) {
            console.error('密码申请失败:', error);
            this.showToast(error.message || '密码申请失败', 'error');

            // 恢复按钮状态
            buttonElement.disabled = false;
            buttonElement.innerHTML = '申请密码';
        }
    }

    /**
     * 显示密码结果
     */
    showPasswordResult(password, remainingRequests) {
        const dialogHtml = `
            <div class="password-result-dialog">
                <div class="dialog-header">
                    <h3>解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-result-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="password-display">
                        <label>解压密码：</label>
                        <div class="password-box">
                            <input type="text" value="${password}" readonly class="password-input">
                            <button class="copy-btn" onclick="fileManager.copyPassword('${password}', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-info">
                        <p><i class="fas fa-info-circle"></i> 请妥善保管此密码，用于解压下载的文件。</p>
                        <p><i class="fas fa-clock"></i> 密码有效期：24小时</p>
                        <p><i class="fas fa-exclamation-triangle"></i> 剩余申请次数：${remainingRequests}</p>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.closest('.password-result-dialog').remove()">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 自动选中密码
        const passwordInput = overlay.querySelector('.password-input');
        if (passwordInput) {
            passwordInput.select();
        }
    }

    /**
     * 复制密码到剪贴板
     */
    async copyPassword(password, buttonElement) {
        try {
            await navigator.clipboard.writeText(password);

            // 更新按钮状态
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i>';
            buttonElement.classList.add('copied');

            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
                buttonElement.classList.remove('copied');
            }, 2000);

            this.showToast('密码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('复制失败，请手动复制', 'error');
        }
    }

    /**
     * 切换视图
     */
    switchView(view) {
        this.currentView = view;

        // 更新导航状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-view="${view}"]`)?.closest('.menu-item');
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // 根据视图显示不同内容
        switch (view) {
            case 'downloads':
                this.showDownloadRecords();
                break;
            case 'favorites':
                this.showFavorites();
                break;
            case 'recent':
                this.showRecentFiles();
                break;
            default:
                this.showHomeView();
                break;
        }
    }

    /**
     * 显示下载记录
     */
    async showDownloadRecords() {
        try {
            // 更新面包屑
            this.updateBreadcrumb([
                { name: '首页', icon: 'fas fa-home' },
                { name: '下载记录', icon: 'fas fa-download' }
            ]);

            // 获取下载记录
            const records = await this.getDownloadRecords();

            // 显示下载记录界面
            this.renderDownloadRecords(records);

        } catch (error) {
            console.error('获取下载记录失败:', error);
            this.showToast('获取下载记录失败', 'error');
        }
    }

    /**
     * 获取下载记录
     */
    async getDownloadRecords() {
        try {
            const response = await fetch(`${CONFIG.API.BASE_URL}/download/records`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) {
                throw new Error(`获取下载记录失败: ${response.status}`);
            }

            const result = await response.json();
            return result.records || [];
        } catch (error) {
            console.error('获取下载记录失败:', error);
            return [];
        }
    }

    /**
     * 渲染下载记录
     */
    renderDownloadRecords(records) {
        const fileGrid = document.getElementById('file-grid');

        if (!records || records.length === 0) {
            fileGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-download"></i>
                    <h3>暂无下载记录</h3>
                    <p>您还没有下载过任何文件</p>
                </div>
            `;
            return;
        }

        // 按日期分组下载记录
        const groupedRecords = this.groupRecordsByDate(records);

        let html = '<div class="download-records-container">';

        for (const [date, dateRecords] of Object.entries(groupedRecords)) {
            html += `
                <div class="download-group">
                    <h3 class="download-date">${this.formatDate(date)}</h3>
                    <div class="download-items">
            `;

            dateRecords.forEach(record => {
                html += this.renderDownloadRecord(record);
            });

            html += `
                    </div>
                </div>
            `;
        }

        html += '</div>';
        fileGrid.innerHTML = html;
    }

    /**
     * 渲染单个下载记录
     */
    renderDownloadRecord(record) {
        const isEncrypted = record.is_encrypted;
        const downloadCount = record.download_count || 0;
        const maxDownloads = 3; // 从配置获取

        return `
            <div class="download-record-card" data-record-id="${record.id}">
                <div class="record-header">
                    <div class="record-icon">
                        <i class="fas fa-file-archive"></i>
                        ${isEncrypted ? '<i class="fas fa-lock encrypted-badge"></i>' : ''}
                    </div>
                    <div class="record-info">
                        <h4 class="record-filename">${record.filename}</h4>
                        <div class="record-meta">
                            <span class="record-size">${this.formatFileSize(record.file_size)}</span>
                            <span class="record-time">${this.formatTime(record.download_time)}</span>
                        </div>
                    </div>
                </div>

                <div class="record-stats">
                    <div class="download-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${(downloadCount / maxDownloads) * 100}%"></div>
                        </div>
                        <span class="progress-text">${downloadCount}/${maxDownloads} 次下载</span>
                    </div>

                    ${isEncrypted ? `
                        <div class="encryption-status">
                            <i class="fas fa-shield-alt"></i>
                            <span>已加密</span>
                        </div>
                    ` : ''}
                </div>

                <div class="record-actions">
                    ${isEncrypted ? `
                        <button class="btn btn-primary btn-sm" onclick="fileManager.requestPasswordForRecord('${record.id}', '${record.filename}')">
                            <i class="fas fa-key"></i>
                            申请密码
                        </button>
                    ` : `
                        <button class="btn btn-secondary btn-sm" onclick="fileManager.redownloadFile('${record.file_id}')">
                            <i class="fas fa-download"></i>
                            重新下载
                        </button>
                    `}

                    <button class="btn btn-outline btn-sm" onclick="fileManager.showRecordDetails('${record.id}')">
                        <i class="fas fa-info-circle"></i>
                        详情
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 按日期分组记录
     */
    groupRecordsByDate(records) {
        const groups = {};

        records.forEach(record => {
            const date = new Date(record.download_time).toDateString();
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(record);
        });

        return groups;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return '今天';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return '昨天';
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    }

    /**
     * 格式化时间
     */
    formatTime(timeString) {
        const date = new Date(timeString);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 为记录申请密码
     */
    async requestPasswordForRecord(recordId, filename) {
        this.showPasswordRequestDialog(recordId, filename, true);
    }

    /**
     * 重新下载文件
     */
    async redownloadFile(fileId) {
        this.downloadFile(fileId);
    }

    /**
     * 显示记录详情
     */
    async showRecordDetails(recordId) {
        // TODO: 实现记录详情显示
        this.showToast('记录详情功能开发中', 'info');
    }

    /**
     * 刷新下载记录
     */
    async refreshDownloadRecords() {
        if (this.currentView === 'downloads') {
            this.showDownloadRecords();
        }
    }

    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(items) {
        const breadcrumbNav = document.querySelector('.breadcrumb-nav');
        if (!breadcrumbNav) return;

        let html = '';
        items.forEach((item, index) => {
            if (index === items.length - 1) {
                html += `
                    <span class="breadcrumb-item current">
                        <i class="${item.icon}"></i>
                        ${item.name}
                    </span>
                `;
            } else {
                html += `
                    <a href="#" class="breadcrumb-item" onclick="fileManager.switchView('${item.view || 'home'}')">
                        <i class="${item.icon}"></i>
                        ${item.name}
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                `;
            }
        });

        breadcrumbNav.innerHTML = html;
    }

    /**
     * 显示主页视图
     */
    showHomeView() {
        // 恢复原来的文件浏览界面
        this.updateBreadcrumb([
            { name: '首页', icon: 'fas fa-home' }
        ]);

        // 重新加载文件列表
        this.loadFiles();
    }

    /**
     * 显示收藏夹
     */
    async showFavorites() {
        // TODO: 实现收藏夹显示
        this.updateBreadcrumb([
            { name: '首页', icon: 'fas fa-home' },
            { name: '收藏夹', icon: 'fas fa-star' }
        ]);

        this.showToast('收藏夹功能开发中', 'info');
    }

    /**
     * 显示最近文件
     */
    async showRecentFiles() {
        // TODO: 实现最近文件显示
        this.updateBreadcrumb([
            { name: '首页', icon: 'fas fa-home' },
            { name: '最近访问', icon: 'fas fa-clock' }
        ]);

        this.showToast('最近访问功能开发中', 'info');
    }
}

// 创建全局文件管理器实例
let fileManager;

document.addEventListener('DOMContentLoaded', () => {
    // 只有在没有被app.js初始化时才创建实例
    if (!window.fileManager && !window.app) {
        fileManager = new FileManager();
        window.fileManager = fileManager;
    }
});

// 全局可用
window.FileManager = FileManager;
