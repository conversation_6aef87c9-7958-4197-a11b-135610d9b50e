<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .record-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .record-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .record-meta {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔽 下载记录功能测试</h1>
            <p>测试下载记录API的各种功能</p>
        </div>

        <div class="test-section">
            <h3>1. 登录测试</h3>
            <button onclick="testLogin()">使用fjj账户登录</button>
            <button onclick="testLoginAdmin()">使用admin账户登录</button>
            <button onclick="checkToken()">检查当前Token</button>
            <div id="loginResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 下载记录API测试</h3>
            <button onclick="testDownloadRecords()">获取下载记录</button>
            <button onclick="testDownloadRecordsWithAuth()">使用Token获取下载记录</button>
            <div id="downloadResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 下载记录显示</h3>
            <button onclick="renderDownloadRecords()">渲染下载记录</button>
            <div id="recordsDisplay"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let currentToken = localStorage.getItem('token');

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'fjj',
                        password: '123456'
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    currentToken = result.token;
                    localStorage.setItem('token', currentToken);
                    showResult('loginResult', `登录成功！\nToken: ${currentToken}\n用户信息: ${JSON.stringify(result.user, null, 2)}`, 'success');
                } else {
                    showResult('loginResult', `登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录请求失败: ${error.message}`, 'error');
            }
        }

        async function testLoginAdmin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    currentToken = result.token;
                    localStorage.setItem('token', currentToken);
                    showResult('loginResult', `管理员登录成功！\nToken: ${currentToken}\n用户信息: ${JSON.stringify(result.user, null, 2)}`, 'success');
                } else {
                    showResult('loginResult', `管理员登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `管理员登录请求失败: ${error.message}`, 'error');
            }
        }

        function checkToken() {
            const token = localStorage.getItem('token');
            if (token) {
                showResult('loginResult', `当前Token: ${token}`, 'info');
            } else {
                showResult('loginResult', '没有找到Token，请先登录', 'error');
            }
        }

        async function testDownloadRecords() {
            try {
                const response = await fetch(`${API_BASE}/download/records`);
                const result = await response.json();
                
                showResult('downloadResult', `无认证请求结果:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('downloadResult', `请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadRecordsWithAuth() {
            if (!currentToken) {
                showResult('downloadResult', '请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/records`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const result = await response.json();
                
                showResult('downloadResult', `认证请求结果:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');
                
                // 如果成功，保存结果用于渲染
                if (response.ok && result.records) {
                    window.lastRecords = result.records;
                }
            } catch (error) {
                showResult('downloadResult', `认证请求失败: ${error.message}`, 'error');
            }
        }

        function renderDownloadRecords() {
            const records = window.lastRecords;
            const display = document.getElementById('recordsDisplay');
            
            if (!records || records.length === 0) {
                display.innerHTML = '<p>没有下载记录数据，请先获取下载记录</p>';
                return;
            }

            let html = '<h4>下载记录列表:</h4>';
            records.forEach(record => {
                html += `
                    <div class="record-card">
                        <div class="record-header">
                            📁 ${record.filename || '未知文件'}
                        </div>
                        <div class="record-meta">
                            <strong>文件ID:</strong> ${record.file_id}<br>
                            <strong>下载时间:</strong> ${record.download_time || '未知'}<br>
                            <strong>文件大小:</strong> ${formatFileSize(record.file_size || 0)}<br>
                            <strong>下载类型:</strong> ${record.download_type || '未知'}<br>
                            <strong>是否加密:</strong> ${record.is_encrypted ? '是' : '否'}<br>
                            <strong>下载次数:</strong> ${record.download_count || 0}
                        </div>
                    </div>
                `;
            });
            
            display.innerHTML = html;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时检查Token
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
